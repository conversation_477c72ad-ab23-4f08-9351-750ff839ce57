package com.hakcu.evmodbus.services;

import com.hakcu.evmodbus.dto.ChartConfiguration;
import com.hakcu.evmodbus.dto.TimePeriodData;
import com.hakcu.evmodbus.entities.Spot;
import com.hakcu.evmodbus.utils.DateTimeUtils;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Locale;
import java.util.Map;

/**
 * Business service for statistics operations.
 * Handles complex business logic for statistics calculations and chart generation.
 */
@Service
public class StatisticsBusinessService {

    private final StatisticsService statisticsService;

    public StatisticsBusinessService(StatisticsService statisticsService) {
        this.statisticsService = statisticsService;
    }

    /**
     * Checks if a spot has any readings in the database.
     *
     * @param spotId The ID of the spot to check
     * @return true if the spot has at least one reading, false otherwise
     */
    public boolean hasAnyReadings(Long spotId) {
        LocalDateTime veryOldDate = LocalDateTime.of(2000, 1, 1, 0, 0);
        LocalDateTime farFutureDate = LocalDateTime.of(2100, 12, 31, 23, 59, 59);
        Float totalConsumption = statisticsService.getTotalConsumption(spotId, veryOldDate, farFutureDate);
        return totalConsumption > 0;
    }

    /**
     * Calculates current period consumption values.
     *
     * @param spotId The ID of the spot
     * @return Map containing today, this month, and this year consumption
     */
    public Map<String, Float> calculateCurrentPeriodConsumption(Long spotId) {
        Map<String, Float> consumption = new HashMap<>();
        LocalDate today = LocalDate.now();

        // Today's consumption
        LocalDateTime startOfDay = today.atStartOfDay();
        LocalDateTime endOfDay = today.atTime(LocalTime.MAX);
        consumption.put("today", statisticsService.getTotalConsumption(spotId, startOfDay, endOfDay));

        // This month's consumption
        LocalDateTime startOfMonth = today.withDayOfMonth(1).atStartOfDay();
        consumption.put("thisMonth", statisticsService.getTotalConsumption(spotId, startOfMonth, endOfDay));

        // This year's consumption
        LocalDateTime startOfYear = today.withDayOfMonth(1).withMonth(1).atStartOfDay();
        consumption.put("thisYear", statisticsService.getTotalConsumption(spotId, startOfYear, endOfDay));

        return consumption;
    }

    /**
     * Calculates the total consumption from a data map.
     *
     * @param data Map of time periods to consumption values
     * @return Total consumption sum
     */
    private float calculateTotalFromData(Map<String, Float> data) {
        if (data == null || data.isEmpty()) {
            return 0.0f;
        }
        return data.values().stream()
            .filter(value -> value != null)
            .reduce(0.0f, Float::sum);
    }

    /**
     * Calculates daily total using the same method as the static summary.
     * For current day, uses the same time range as calculateCurrentPeriodConsumption.
     * For historical days, uses the full day range.
     */
    private float calculateDailyTotal(Long spotId, TimePeriodData timePeriod) {
        LocalDateTime dayStart = timePeriod.selectedDay().atStartOfDay();
        LocalDateTime dayEnd;

        if (timePeriod.isCurrentDay()) {
            // Use same end time as static summary for current day
            dayEnd = LocalDate.now().atTime(LocalTime.MAX);
        } else {
            // Use full day for historical days
            dayEnd = timePeriod.selectedDay().atTime(LocalTime.MAX);
        }

        return statisticsService.getTotalConsumption(spotId, dayStart, dayEnd);
    }

    /**
     * Calculates monthly total using the same method as the static summary.
     * For current month, uses the same time range as calculateCurrentPeriodConsumption.
     * For historical months, uses the full month range.
     */
    private float calculateMonthlyTotal(Long spotId, TimePeriodData timePeriod) {
        YearMonth yearMonth = timePeriod.getYearMonth();
        LocalDateTime monthStart = yearMonth.atDay(1).atStartOfDay();
        LocalDateTime monthEnd;

        if (timePeriod.isCurrentMonth()) {
            // Use same end time as static summary for current month
            monthEnd = LocalDate.now().atTime(LocalTime.MAX);
        } else {
            // Use full month for historical months
            monthEnd = yearMonth.atEndOfMonth().atTime(LocalTime.MAX);
        }

        return statisticsService.getTotalConsumption(spotId, monthStart, monthEnd);
    }

    /**
     * Calculates yearly total using the same method as the static summary.
     * For current year, uses the same time range as calculateCurrentPeriodConsumption.
     * For historical years, uses the full year range.
     */
    private float calculateYearlyTotal(Long spotId, TimePeriodData timePeriod) {
        LocalDateTime yearStart = LocalDate.of(timePeriod.selectedYear(), 1, 1).atStartOfDay();
        LocalDateTime yearEnd;

        if (timePeriod.isCurrentYear()) {
            // Use same end time as static summary for current year
            yearEnd = LocalDate.now().atTime(LocalTime.MAX);
        } else {
            // Use full year for historical years
            yearEnd = LocalDate.of(timePeriod.selectedYear(), 12, 31).atTime(LocalTime.MAX);
        }

        return statisticsService.getTotalConsumption(spotId, yearStart, yearEnd);
    }

    /**
     * Generates hourly chart data and adds it to the model.
     */
    public void generateHourlyChart(Model model, Long spotId, TimePeriodData timePeriod, ChartConfiguration chartConfig) {
        LocalDateTime dayStart = timePeriod.selectedDay().atStartOfDay();
        LocalDateTime dayEnd = timePeriod.isCurrentDay() ? LocalDateTime.now() :
            timePeriod.selectedDay().atTime(LocalTime.MAX);

        Map<String, Float> hourlyData = statisticsService.getHourlyConsumption(spotId, dayStart, dayEnd);

        // Filter current day data if needed
        if (timePeriod.isCurrentDay()) {
            hourlyData = filterCurrentDayData(hourlyData);
        }

        String title = buildHourlyChartTitle(timePeriod);
        String chartSvg = statisticsService.generateBarChartSvg(hourlyData, chartConfig.width(),
            chartConfig.height(), title, chartConfig.yAxisLabel());

        // Calculate filtered total for the selected day
        float dailyTotal = calculateDailyTotal(spotId, timePeriod);

        model.addAttribute("hourlyBarChartSvg", chartSvg);
        model.addAttribute("hourlyData", hourlyData);
        model.addAttribute("isCurrentDay", timePeriod.isCurrentDay());
        model.addAttribute("dailyTotal", dailyTotal);
    }

    /**
     * Generates daily chart data and adds it to the model.
     */
    public void generateDailyChart(Model model, Long spotId, TimePeriodData timePeriod, ChartConfiguration chartConfig) {
        YearMonth yearMonth = timePeriod.getYearMonth();
        LocalDateTime monthStart = yearMonth.atDay(1).atStartOfDay();
        LocalDateTime monthEnd = timePeriod.isCurrentMonth() ? LocalDateTime.now() :
            yearMonth.atEndOfMonth().atTime(LocalTime.MAX);

        Map<String, Float> dailyData = statisticsService.getDailyConsumption(spotId, monthStart, monthEnd);

        // Filter current month data if needed
        if (timePeriod.isCurrentMonth()) {
            dailyData = filterCurrentMonthData(dailyData);
        }

        String title = buildDailyChartTitle(yearMonth, timePeriod.isCurrentMonth());
        String chartSvg = statisticsService.generateBarChartSvg(dailyData, chartConfig.width(),
            chartConfig.height(), title, chartConfig.yAxisLabel());

        // Calculate filtered total for the selected month
        float monthlyTotal = calculateMonthlyTotal(spotId, timePeriod);

        model.addAttribute("dailyBarChartSvg", chartSvg);
        model.addAttribute("dailyData", dailyData);
        model.addAttribute("isCurrentMonth", timePeriod.isCurrentMonth());
        model.addAttribute("monthlyTotal", monthlyTotal);
    }

    /**
     * Generates monthly chart data and adds it to the model.
     */
    public void generateMonthlyChart(Model model, Long spotId, TimePeriodData timePeriod, ChartConfiguration chartConfig) {
        LocalDateTime yearStart = LocalDate.of(timePeriod.selectedYear(), 1, 1).atStartOfDay();
        LocalDateTime yearEnd = LocalDate.of(timePeriod.selectedYear(), 12, 31).atTime(LocalTime.MAX);

        Map<String, Float> monthlyData = statisticsService.getMonthlyConsumption(spotId, yearStart, yearEnd);

        // Filter current year data if needed
        if (timePeriod.isCurrentYear()) {
            monthlyData = filterCurrentYearData(monthlyData);
        }

        String title = buildMonthlyChartTitle(timePeriod.selectedYear(), timePeriod.isCurrentYear());
        String chartSvg = statisticsService.generateBarChartSvg(monthlyData, chartConfig.width(),
            chartConfig.height(), title, chartConfig.yAxisLabel());

        // Calculate filtered total for the selected year
        float yearlyTotal = calculateYearlyTotal(spotId, timePeriod);

        model.addAttribute("monthlyBarChartSvg", chartSvg);
        model.addAttribute("monthlyData", monthlyData);
        model.addAttribute("isCurrentYear", timePeriod.isCurrentYear());
        model.addAttribute("yearlyTotal", yearlyTotal);
    }

    /**
     * Adds date range attributes to the model for readings links.
     */
    public void addDateRangeAttributes(Model model, TimePeriodData timePeriod) {
        // Daily readings date range
        LocalDateTime[] dailyDateRange = DateTimeUtils.calculateDailyDateTimeRange(
            timePeriod.selectedDay(), timePeriod.isCurrentDay());
        model.addAttribute("dailyStartDateTime", dailyDateRange[0]);
        model.addAttribute("dailyEndDateTime", dailyDateRange[1]);

        // Monthly readings date range
        LocalDateTime[] monthlyDateRange = DateTimeUtils.calculateMonthlyDateTimeRange(
            timePeriod.getYearMonth(), timePeriod.isCurrentMonth());
        model.addAttribute("monthlyStartDateTime", monthlyDateRange[0]);
        model.addAttribute("monthlyEndDateTime", monthlyDateRange[1]);

        // Yearly readings date range
        LocalDateTime[] yearlyDateRange = DateTimeUtils.calculateYearlyDateTimeRange(
            timePeriod.selectedYear(), timePeriod.isCurrentYear());
        model.addAttribute("yearlyStartDateTime", yearlyDateRange[0]);
        model.addAttribute("yearlyEndDateTime", yearlyDateRange[1]);
    }

    /**
     * Gets a spot by ID, throwing EntityNotFoundException if not found.
     */
    public Spot getSpotById(Long spotId) {
        return statisticsService.getSpotById(spotId)
            .orElseThrow(() -> new EntityNotFoundException("Spot not found"));
    }

    private Map<String, Float> filterCurrentDayData(Map<String, Float> hourlyData) {
        int currentHour = LocalTime.now().getHour();
        Map<String, Float> filteredData = new LinkedHashMap<>();

        hourlyData.entrySet().stream()
            .filter(entry -> {
                String hourStr = entry.getKey().substring(0, 2);
                int hour = Integer.parseInt(hourStr);
                return hour <= currentHour;
            })
            .forEach(entry -> filteredData.put(entry.getKey(), entry.getValue()));

        return filteredData;
    }

    private Map<String, Float> filterCurrentMonthData(Map<String, Float> dailyData) {
        int currentDay = LocalDate.now().getDayOfMonth();
        Map<String, Float> filteredData = new LinkedHashMap<>();

        dailyData.entrySet().stream()
            .filter(entry -> {
                String dayStr = entry.getKey().substring(0, 2);
                int day = Integer.parseInt(dayStr);
                return day <= currentDay;
            })
            .forEach(entry -> filteredData.put(entry.getKey(), entry.getValue()));

        return filteredData;
    }

    private Map<String, Float> filterCurrentYearData(Map<String, Float> monthlyData) {
        int currentMonth = LocalDate.now().getMonthValue();
        Map<String, Float> filteredData = new LinkedHashMap<>();

        // Create month name to number mapping
        Map<String, Integer> monthNameToNumber = new HashMap<>();
        for (int i = 1; i <= 12; i++) {
            String monthName = Month.of(i).getDisplayName(TextStyle.SHORT, Locale.of("es", "ES"));
            monthName = monthName.substring(0, 1).toUpperCase() + monthName.substring(1);
            monthNameToNumber.put(monthName, i);
        }

        monthlyData.entrySet().stream()
            .filter(entry -> {
                Integer monthNumber = monthNameToNumber.get(entry.getKey());
                return monthNumber != null && monthNumber <= currentMonth;
            })
            .forEach(entry -> filteredData.put(entry.getKey(), entry.getValue()));

        return filteredData;
    }

    private String buildHourlyChartTitle(TimePeriodData timePeriod) {
        String title = "Consumo por hora - " +
            statisticsService.getFormattedDayForTitle(timePeriod.selectedDay().toString());
        if (timePeriod.isCurrentDay()) {
            title += " (hasta " + LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm")) + ")";
        }
        return title;
    }

    private String buildDailyChartTitle(YearMonth yearMonth, boolean isCurrentMonth) {
        String title = "Consumo diario - " +
            yearMonth.getMonth().getDisplayName(TextStyle.FULL, Locale.of("es", "ES")) + " " + yearMonth.getYear();
        if (isCurrentMonth) {
            title += " (hasta " + LocalDate.now().format(DateTimeFormatter.ofPattern("dd/MM")) + ")";
        }
        return title;
    }

    private String buildMonthlyChartTitle(Integer selectedYear, boolean isCurrentYear) {
        String title = "Consumo mensual - " + selectedYear;
        if (isCurrentYear) {
            YearMonth currentYearMonth = YearMonth.now();
            title += " (hasta " + currentYearMonth.getMonth().getDisplayName(TextStyle.FULL, Locale.of("es", "ES")) + ")";
        }
        return title;
    }
}
