<!DOCTYPE html>
<html lang="es" xmlns:th="http://www.thymeleaf.org">
<body>

<!-- Chart Tab Fragment -->
<th:block th:fragment="chart-tab(id, title, icon, chartSvg, currentPeriod, currentPeriodText, formatPattern, useSpanishLocale)">
    <div class="tab-pane fade" th:id="${id + '-content'}" th:classappend="${id == (activeTab != null ? activeTab : 'hourly') ? 'show active' : ''}"
         role="tabpanel" th:aria-labelledby="${id + '-tab'}">

        <!-- Filtered Period Total - Show when data exists -->
        <div class="row my-3" th:if="${(id == 'hourly' && hourlyData != null && !hourlyData.isEmpty()) || (id == 'daily' && dailyData != null && !dailyData.isEmpty()) || (id == 'monthly' && monthlyData != null && !monthlyData.isEmpty())}">
            <div class="col-12">
                <div class="alert alert-info mb-0 py-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <!-- Hourly Tab: Daily Total -->
                            <span th:if="${id == 'hourly'}" class="fw-bold">
                                <i class="bi bi-calendar-day me-2"></i>Total del día:
                                <span th:text="${#numbers.formatDecimal(dailyTotal != null ? dailyTotal : 0, 1, 2) + ' kWh'}">0.00 kWh</span>
                            </span>
                            <!-- Daily Tab: Monthly Total -->
                            <span th:if="${id == 'daily'}" class="fw-bold">
                                <i class="bi bi-calendar-month me-2"></i>Total del mes:
                                <span th:text="${#numbers.formatDecimal(monthlyTotal != null ? monthlyTotal : 0, 1, 2) + ' kWh'}">0.00 kWh</span>
                            </span>
                            <!-- Monthly Tab: Yearly Total -->
                            <span th:if="${id == 'monthly'}" class="fw-bold">
                                <i class="bi bi-calendar-range me-2"></i>Total del año:
                                <span th:text="${#numbers.formatDecimal(yearlyTotal != null ? yearlyTotal : 0, 1, 2) + ' kWh'}">0.00 kWh</span>
                            </span>
                        </div>
                        <div class="text-muted small">
                            <!-- Show period description -->
                            <span th:if="${id == 'hourly'}" th:text="${#temporals.format(selectedDay, 'dd/MM/yyyy')}"></span>
                            <span th:if="${id == 'daily'}" th:with="yearMonth=${T(java.time.YearMonth).parse(selectedMonth)}"
                                  th:text="${yearMonth.getMonth().getDisplayName(T(java.time.format.TextStyle).FULL, T(java.util.Locale).forLanguageTag('es'))} + ' ' + ${yearMonth.getYear()}"></span>
                            <span th:if="${id == 'monthly'}" th:text="'Año ' + ${selectedYear}"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Show chart if data exists -->
        <div class="chart-container py-0 mb-0" th:if="${(id == 'hourly' && hourlyData != null && !hourlyData.isEmpty()) || (id == 'daily' && dailyData != null && !dailyData.isEmpty()) || (id == 'monthly' && monthlyData != null && !monthlyData.isEmpty())}" th:utext="${chartSvg}"></div>

        <!-- Show "No data available" placeholder when no data exists for the selected date range -->
        <div class="chart-container py-0 mb-0 d-flex align-items-center justify-content-center" th:if="${(id == 'hourly' && (hourlyData == null || hourlyData.isEmpty())) || (id == 'daily' && (dailyData == null || dailyData.isEmpty())) || (id == 'monthly' && (monthlyData == null || monthlyData.isEmpty()))}">
            <div class="text-center p-5">
                <i class="bi bi-bar-chart text-muted" style="font-size: 3rem;"></i>
                <h5 class="mt-3 text-muted">No data available for the selected date range</h5>
                <p class="text-muted">Utilice los controles de navegación para ver otros períodos</p>
            </div>
        </div>
    </div>
</th:block>

<!-- Tab Button Fragment -->
<th:block th:fragment="tab-button(id, title, icon, isActive, currentPeriod, currentPeriodText, formatPattern, useSpanishLocale)">
    <li class="nav-item flex-fill" role="presentation">
        <button class="nav-link w-100 d-flex align-items-center justify-content-center" th:id="${id + '-tab'}" th:classappend="${id == (activeTab != null ? activeTab : 'hourly') ? 'active' : ''}"
                data-bs-toggle="tab" th:data-bs-target="${'#' + id + '-content'}"
                type="button" role="tab" th:aria-controls="${id + '-content'}" th:aria-selected="${id == (activeTab != null ? activeTab : 'hourly')}"
                th:hx-get="@{/statistics/tab-switch(tabId=${id}, spotId=${selectedSpotId}, selectedDay=${selectedDay}, selectedMonth=${selectedMonth}, selectedYear=${selectedYear})}"
                hx-target="#filter-container"
                hx-swap="outerHTML"
                hx-push-url="false">
            <div>
                <i class="bi" th:classappend="${'bi-' + icon + ' me-2'}"></i>
                <span th:text="${title}"></span>
                <!-- Desktop view (inline) -->
                <span class="d-none d-md-inline small text-muted" th:if="${currentPeriod}">
                    (hasta <span th:if="${useSpanishLocale}" th:text="${#temporals.format(#temporals.createNow(), formatPattern, new java.util.Locale('es'))}"></span><span th:unless="${useSpanishLocale}" th:text="${#temporals.format(#temporals.createNow(), formatPattern)}"></span>)
                </span>
                <!-- Mobile view (block) -->
                <div class="d-md-none small text-muted" th:if="${currentPeriod}">
                    (hasta <span th:if="${useSpanishLocale}" th:text="${#temporals.format(#temporals.createNow(), formatPattern, new java.util.Locale('es'))}"></span><span th:unless="${useSpanishLocale}" th:text="${#temporals.format(#temporals.createNow(), formatPattern)}"></span>)
                </div>
            </div>
        </button>
    </li>
</th:block>

<!-- Filter Input Fragment -->
<th:block th:fragment="filter-input(id, type, icon, label, inputId, inputValue, spotId, otherParams)">
    <div class="mb-2 mb-md-0" th:id="${id + '-filter'}" th:style="${id != (activeTab != null ? activeTab : 'hourly') ? 'display: none;' : ''}">
        <div class="input-group">
            <span class="input-group-text"><i class="bi" th:classappend="${'bi-' + icon + ' me-1'}"></i><span th:text="${label}"></span></span>
            <input class="form-control" th:hx-include="${'#' + inputId}" hx-push-url="true" hx-swap="innerHTML"
                   hx-target="body"
                   hx-trigger="change"
                   th:id="${inputId}"
                   th:name="${inputId}"
                   th:attr="hx-get=@{/statistics(spotId=${spotId}, activeTab=${id}, __${otherParams}__)}"
                   th:value="${inputValue}"
                   th:type="${type}">
        </div>
    </div>
</th:block>

<!-- Download Button Fragment -->
<th:block th:fragment="download-button(id, reportType, paramName, paramValue, spotId, label)">
    <a class="btn btn-outline-primary mb-2 mb-md-0 w-100 w-md-auto" th:id="${id + '-download'}" th:style="${id != (activeTab != null ? activeTab : 'hourly') ? 'display: none;' : ''}"
       th:href="@{/statistics/pdf(spotId=${spotId}, reportType=${reportType}, __${paramName}__=${paramValue})}"
       target="_blank" th:title="${'Descargar PDF ' + label}">
        <i class="bi bi-file-earmark-pdf-fill text-danger me-1"></i> <span th:text="${'Informe ' + label}"></span>
    </a>
</th:block>

</body>
</html>
